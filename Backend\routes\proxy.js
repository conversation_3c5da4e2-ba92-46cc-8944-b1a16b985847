const express = require('express');
const { protect } = require('../middleware/auth');
const {
  serveContent,
  servePreview,
  streamContent,
  serveThumbnail
} = require('../controllers/proxy');

const router = express.Router();

// All proxy routes require authentication
router.use(protect);

// Content proxy routes
router.get('/content/:contentId', serveContent);
router.get('/preview/:contentId', servePreview);
router.get('/stream/:contentId', streamContent);
router.get('/thumbnail/:contentId', serveThumbnail);

module.exports = router;
