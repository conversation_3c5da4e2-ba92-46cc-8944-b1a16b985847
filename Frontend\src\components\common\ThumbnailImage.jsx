import React, { useState } from 'react';
import { 
  getProxyThumbnailUrl, 
  getProxyUrlWithAuth, 
  getSmartFileUrl,
  getPlaceholderImage 
} from '../../utils/constants';

/**
 * ThumbnailImage component that handles proxy URLs with fallbacks
 * @param {Object} props
 * @param {string} props.contentId - Content ID for proxy URL generation
 * @param {string} props.thumbnailUrl - Direct thumbnail URL as fallback
 * @param {string} props.alt - Alt text for the image
 * @param {Object} props.style - Inline styles for the image
 * @param {string} props.className - CSS class name
 * @param {number} props.placeholderWidth - Placeholder width
 * @param {number} props.placeholderHeight - Placeholder height
 * @param {string} props.placeholderText - Placeholder text
 * @param {Function} props.onError - Custom error handler
 * @param {Function} props.onLoad - Custom load handler
 */
const ThumbnailImage = ({
  contentId,
  thumbnailUrl,
  alt = "Thumbnail",
  style = {},
  className = "",
  placeholderWidth = 200,
  placeholderHeight = 120,
  placeholderText = "No Image",
  onError,
  onLoad,
  ...props
}) => {
  const [imageError, setImageError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(null);

  // Generate the best available image source
  const getImageSrc = () => {
    if (imageError) {
      return getPlaceholderImage(placeholderWidth, placeholderHeight, placeholderText);
    }

    // Priority 1: Use proxy URL if content ID is available
    if (contentId) {
      const proxyUrl = getProxyThumbnailUrl(contentId);
      if (proxyUrl) {
        return getProxyUrlWithAuth(proxyUrl);
      }
    }

    // Priority 2: Use direct thumbnail URL
    if (thumbnailUrl) {
      return getSmartFileUrl(thumbnailUrl);
    }

    // Priority 3: Use placeholder
    return getPlaceholderImage(placeholderWidth, placeholderHeight, placeholderText);
  };

  const handleImageError = (e) => {
    console.warn('Thumbnail image failed to load:', {
      contentId,
      thumbnailUrl,
      currentSrc: e.target.src,
      error: e
    });

    // If we haven't tried the fallback yet, try it
    if (!imageError) {
      setImageError(true);
      
      // Try fallback URL if we were using proxy
      if (contentId && thumbnailUrl) {
        const fallbackUrl = getSmartFileUrl(thumbnailUrl);
        setCurrentSrc(fallbackUrl);
        e.target.src = fallbackUrl;
        return; // Don't set placeholder yet, try fallback first
      }
    }

    // Set placeholder as final fallback
    const placeholderUrl = getPlaceholderImage(placeholderWidth, placeholderHeight, placeholderText);
    e.target.src = placeholderUrl;
    setCurrentSrc(placeholderUrl);

    // Call custom error handler if provided
    if (onError) {
      onError(e);
    }
  };

  const handleImageLoad = (e) => {
    // Reset error state on successful load
    if (imageError) {
      setImageError(false);
    }

    // Call custom load handler if provided
    if (onLoad) {
      onLoad(e);
    }
  };

  const imageSrc = currentSrc || getImageSrc();

  return (
    <img
      src={imageSrc}
      alt={alt}
      style={style}
      className={className}
      onError={handleImageError}
      onLoad={handleImageLoad}
      {...props}
    />
  );
};

export default ThumbnailImage;
