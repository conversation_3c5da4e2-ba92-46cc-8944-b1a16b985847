import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { getSellerOffers, updateOfferStatus } from "../../redux/slices/offerSlice";
import LoadingSkeleton from "../common/LoadingSkeleton";
import { ErrorDisplay } from "../common/ErrorBoundary";
import "../../styles/SellerOfferManagement.css";
import { FaEye, FaCheckCircle, FaTimes } from 'react-icons/fa';
import { formatStandardDate } from "../../utils/dateValidation";
import {
  getSmartFileUrl,
  getPlaceholderImage,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth
} from "../../utils/constants";

const SellerOfferManagement = () => {
  const dispatch = useDispatch();
  const { sellerOffers, isLoading, isError, error } = useSelector((state) => state.offer);

  const [selectedOffer, setSelectedOffer] = useState(null);
  const [responseMessage, setResponseMessage] = useState("");
  const [isResponding, setIsResponding] = useState(false);

  useEffect(() => {
    dispatch(getSellerOffers());
  }, [dispatch]);

  const handleAcceptOffer = async (offerId) => {
    setIsResponding(true);
    try {
      await dispatch(updateOfferStatus({
        offerId,
        status: "accepted",
        sellerResponse: responseMessage
      })).unwrap();

      toast.success("Offer accepted successfully!");
      setSelectedOffer(null);
      setResponseMessage("");

      // Refresh offers
      dispatch(getSellerOffers());
    } catch (error) {
      toast.error(error.message || "Failed to accept offer");
    } finally {
      setIsResponding(false);
    }
  };

  const handleRejectOffer = async (offerId) => {
    setIsResponding(true);
    try {
      await dispatch(updateOfferStatus({
        offerId,
        status: "rejected",
        sellerResponse: responseMessage
      })).unwrap();

      toast.success("Offer rejected");
      setSelectedOffer(null);
      setResponseMessage("");

      // Refresh offers
      dispatch(getSellerOffers());
    } catch (error) {
      toast.error(error.message || "Failed to reject offer");
    } finally {
      setIsResponding(false);
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Pending":
        return "status-pending";
      case "Accepted":
        return "status-accepted";
      case "Rejected":
        return "status-rejected";
      case "Cancelled":
        return "status-cancelled";
      case "Expired":
        return "status-expired";
      default:
        return "status-default";
    }
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  if (isLoading) {
    return <LoadingSkeleton type="table" />;
  }

  if (isError) {
    return (
      <ErrorDisplay
        title="Error Loading Offers"
        message={error?.message || "Failed to load offers"}
        onRetry={() => dispatch(getSellerOffers())}
      />
    );
  }

  return (
    <div className="seller-offer-management">
      <div className="offer-management-header">
        <h2>Incoming Offers</h2>
        <p>Manage offers from buyers on your content</p>
      </div>

      {sellerOffers.length === 0 ? (
        <div className="no-offers">
          <h3>No offers yet</h3>
          <p>When buyers make offers on your content, they'll appear here.</p>
        </div>
      ) : (
        <div className="offers-table-container">
          <table className="offers-table">
            <thead>
              <tr>
                <th>Content</th>
                <th>Buyer</th>
                <th>Offer Amount</th>
                <th>Message</th>
                <th>Date</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {sellerOffers.map((offer) => (
                <tr key={offer._id}>
                  <td>
                    <div className="content-info">
                      <img
                        src={offer.content?._id
                          ? getProxyUrlWithAuth(getProxyThumbnailUrl(offer.content._id))
                          : getSmartFileUrl(offer.content?.thumbnailUrl) || getPlaceholderImage(80, 80, "No Image")
                        }
                        alt={offer.content?.title}
                        className="content-thumbnail"
                        onError={(e) => {
                          e.target.src = getPlaceholderImage(80, 80, "No Image");
                        }}
                      />
                      <span className="content-title">{offer.content?.title}</span>
                    </div>
                  </td>
                  <td>
                    <div className="buyer-info">
                      <span className="buyer-name">
                        {offer.buyer?.firstName} {offer.buyer?.lastName}
                      </span>
                      <span className="buyer-email">{offer.buyer?.email}</span>
                    </div>
                  </td>
                  <td className="offer-amount">${offer.amount.toFixed(2)}</td>
                  <td className="offer-message">
                    {offer.message ? (
                      <span title={offer.message}>
                        {offer.message.length > 50
                          ? `${offer.message.substring(0, 50)}...`
                          : offer.message
                        }
                      </span>
                    ) : (
                      <span className="no-message">No message</span>
                    )}
                  </td>
                  <td className="offer-date">{formatDate(offer.createdAt)}</td>
                  <td>
                    <span className={`status-badge ${getStatusBadgeClass(offer.status)}`}>
                      {offer.status}
                    </span>
                  </td>
                  <td className="offer-actions">
                    {offer.status === "Pending" ? (
                      <div className="action-buttons">
                        <button
                          className="btn-accept"
                          onClick={() => setSelectedOffer(offer)}
                          disabled={isResponding}
                        >
                          Review
                        </button>
                      </div>
                    ) : (
                      <span className="action-completed">
                        {offer.status === "Accepted" ? "✓ Accepted" : "✗ Rejected"}
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Offer Review Modal */}
      {selectedOffer && (
        <div className="offer-modal-overlay">
          <div className="offer-review-modal">
            <div className="modal-header">
              <h3>Review Offer</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setSelectedOffer(null);
                  setResponseMessage("");
                }}
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="offer-details">
                <h4>{selectedOffer.content?.title}</h4>
                <p><strong>Buyer:</strong> {selectedOffer.buyer?.firstName} {selectedOffer.buyer?.lastName}</p>
                <p><strong>Offer Amount:</strong> ${selectedOffer.amount.toFixed(2)}</p>
                <p><strong>Listed Price:</strong> ${selectedOffer.content?.price?.toFixed(2)}</p>
                {selectedOffer.message && (
                  <div className="buyer-message">
                    <strong>Buyer's Message:</strong>
                    <p>{selectedOffer.message}</p>
                  </div>
                )}
              </div>

              <div className="response-section">
                <label htmlFor="responseMessage">Your Response (Optional):</label>
                <textarea
                  id="responseMessage"
                  value={responseMessage}
                  onChange={(e) => setResponseMessage(e.target.value)}
                  placeholder="Add a message to the buyer..."
                  rows="3"
                  maxLength="500"
                />
                <small>{responseMessage.length}/500</small>
              </div>

              <div className="modal-actions">
                <button
                  className="btn-reject"
                  onClick={() => handleRejectOffer(selectedOffer._id)}
                  disabled={isResponding}
                >
                  {isResponding ? "Processing..." : "Reject Offer"}
                </button>
                <button
                  className="btn-accept"
                  onClick={() => handleAcceptOffer(selectedOffer._id)}
                  disabled={isResponding}
                >
                  {isResponding ? "Processing..." : "Accept Offer"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SellerOfferManagement;
