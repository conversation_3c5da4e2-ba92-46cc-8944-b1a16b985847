import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { getSellerBids, updateBidStatus } from "../../redux/slices/bidSlice";
import LoadingSkeleton from "../common/LoadingSkeleton";
import { ErrorDisplay } from "../common/ErrorBoundary";
import "../../styles/SellerBidManagement.css";
import {
  getSmartFileUrl,
  getPlaceholderImage,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth
} from "../../utils/constants";
import { FaEye, FaCheckCircle, FaTimes } from 'react-icons/fa';
import { formatStandardDate } from "../../utils/dateValidation";

const SellerBidManagement = () => {
  const dispatch = useDispatch();
  const { sellerBids, isLoading, isError, error } = useSelector(
    (state) => state.bid
  );

  const [selectedBid, setSelectedBid] = useState(null);
  const [responseMessage, setResponseMessage] = useState("");
  const [isResponding, setIsResponding] = useState(false);

  useEffect(() => {
    dispatch(getSellerBids());
  }, [dispatch]);

  const handleAcceptBid = async (bidId) => {
    setIsResponding(true);
    try {
      await dispatch(
        updateBidStatus({
          bidId,
          status: "accepted",
          sellerResponse: responseMessage,
        })
      ).unwrap();

      toast.success("Bid accepted successfully! Order has been created.");
      setSelectedBid(null);
      setResponseMessage("");

      // Refresh bids
      dispatch(getSellerBids());
    } catch (error) {
      toast.error(error.message || "Failed to accept bid");
    } finally {
      setIsResponding(false);
    }
  };

  const handleRejectBid = async (bidId) => {
    setIsResponding(true);
    try {
      await dispatch(
        updateBidStatus({
          bidId,
          status: "rejected",
          sellerResponse: responseMessage,
        })
      ).unwrap();

      toast.success("Bid rejected");
      setSelectedBid(null);
      setResponseMessage("");

      // Refresh bids
      dispatch(getSellerBids());
    } catch (error) {
      toast.error(error.message || "Failed to reject bid");
    } finally {
      setIsResponding(false);
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Active":
        return "status-active";
      case "Won":
        return "status-won";
      case "Lost":
        return "status-lost";
      case "Cancelled":
        return "status-cancelled";
      default:
        return "status-default";
    }
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const isAuctionActive = (content) => {
    if (!content?.auctionDetails) return false;

    const now = new Date();
    const startDate = content.auctionDetails.auctionStartDate
      ? new Date(content.auctionDetails.auctionStartDate)
      : null;
    const endDate = content.auctionDetails.auctionEndDate
      ? new Date(content.auctionDetails.auctionEndDate)
      : null;

    return (!startDate || now >= startDate) && (!endDate || now <= endDate);
  };

  if (isLoading) {
    return <LoadingSkeleton type="table" />;
  }

  if (isError) {
    return (
      <ErrorDisplay
        title="Error Loading Bids"
        message={error?.message || "Failed to load bids"}
        onRetry={() => dispatch(getSellerBids())}
      />
    );
  }

  const activeBids = sellerBids.filter((bid) => bid.status === "Active");
  const completedBids = sellerBids.filter((bid) => bid.status !== "Active");

  return (
    <div className="seller-bid-management">
      <div className="bid-management-header">
        <h2>Auction Bids</h2>
        <p>Manage bids on your auction content</p>
      </div>

      {/* Active Bids Section */}
      <div className="bids-section">
        <h3>Active Bids ({activeBids.length})</h3>

        {activeBids.length === 0 ? (
          <div className="no-bids">
            <p>No active bids on your auction content.</p>
          </div>
        ) : (
          <div className="bids-table-container">
            <table className="bids-table">
              <thead>
                <tr>
                  <th>Content</th>
                  <th>Bidder</th>
                  <th>Bid Amount</th>
                  <th>Date</th>
                  <th>Auction Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {activeBids.map((bid) => (
                  <tr key={bid._id}>
                    <td>
                      <div className="content-info">
                        <img
                          src={bid.content?._id
                            ? getProxyUrlWithAuth(getProxyThumbnailUrl(bid.content._id))
                            : getSmartFileUrl(bid.content?.thumbnailUrl) || getPlaceholderImage(80, 80, "No Image")
                          }
                          alt={bid.content?.title}
                          className="content-thumbnail"
                          onError={(e) => {
                            e.target.src = getPlaceholderImage(80, 80, "No Image");
                          }}
                        />
                        <span className="content-title">
                          {bid.content?.title}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div className="bidder-info">
                        <span className="bidder-name">
                          {bid.bidder?.firstName} {bid.bidder?.lastName}
                        </span>
                        <span className="bidder-email">
                          {bid.bidder?.email}
                        </span>
                      </div>
                    </td>
                    <td className="bid-amount">${bid.amount.toFixed(2)}</td>
                    <td className="bid-date">{formatDate(bid.createdAt)}</td>
                    <td>
                      <span
                        className={`auction-status ${isAuctionActive(bid.content) ? "active" : "ended"
                          }`}
                      >
                        {isAuctionActive(bid.content) ? "Active" : "Ended"}
                      </span>
                    </td>
                    <td className="bid-actions">
                      <div className="action-buttons">
                        <button
                          className="btn-review"
                          onClick={() => setSelectedBid(bid)}
                          disabled={isResponding}
                        ></button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Completed Bids Section */}
      {completedBids.length > 0 && (
        <div className="bids-section">
          <h3>Completed Bids ({completedBids.length})</h3>

          <div className="bids-table-container">
            <table className="bids-table">
              <thead>
                <tr>
                  <th>Content</th>
                  <th>Bidder</th>
                  <th>Bid Amount</th>
                  <th>Date</th>
                  <th>Status</th>
                  <th>Result</th>
                </tr>
              </thead>
              <tbody>
                {completedBids.map((bid) => (
                  <tr key={bid._id}>
                    <td>
                      <div className="content-info">
                        <img
                          src={bid.content?._id
                            ? getProxyUrlWithAuth(getProxyThumbnailUrl(bid.content._id))
                            : getSmartFileUrl(bid.content?.thumbnailUrl) || getPlaceholderImage(80, 80, "No Image")
                          }
                          alt={bid.content?.title}
                          className="content-thumbnail"
                          onError={(e) => {
                            e.target.src = getPlaceholderImage(80, 80, "No Image");
                          }}
                        />
                        <span className="content-title">
                          {bid.content?.title}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div className="bidder-info">
                        <span className="bidder-name">
                          {bid.bidder?.firstName} {bid.bidder?.lastName}
                        </span>
                        <span className="bidder-email">
                          {bid.bidder?.email}
                        </span>
                      </div>
                    </td>
                    <td className="bid-amount">${bid.amount.toFixed(2)}</td>
                    <td className="bid-date">{formatDate(bid.createdAt)}</td>
                    <td>
                      <span
                        className={`status-badge ${getStatusBadgeClass(
                          bid.status
                        )}`}
                      >
                        {bid.status}
                      </span>
                    </td>
                    <td className="bid-result">
                      {bid.status === "Won"
                        ? "✓ Winning Bid"
                        : "✗ Not Selected"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Bid Review Modal */}
      {selectedBid && (
        <div className="bid-modal-overlay">
          <div className="bid-review-modal">
            <div className="modal-header">
              <h3>Review Bid</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setSelectedBid(null);
                  setResponseMessage("");
                }}
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="bid-details">
                <h4>{selectedBid.content?.title}</h4>
                <p>
                  <strong>Bidder:</strong> {selectedBid.bidder?.firstName}{" "}
                  {selectedBid.bidder?.lastName}
                </p>
                <p>
                  <strong>Bid Amount:</strong> ${selectedBid.amount.toFixed(2)}
                </p>
                <p>
                  <strong>Starting Price:</strong> $
                  {selectedBid.content?.auctionDetails?.basePrice?.toFixed(2) ||
                    "N/A"}
                </p>
                <p>
                  <strong>Auction Status:</strong>{" "}
                  {isAuctionActive(selectedBid.content) ? "Active" : "Ended"}
                </p>
                <p>
                  <strong>Bid Date:</strong> {formatDate(selectedBid.createdAt)}
                </p>
              </div>

              <div className="response-section">
                <label htmlFor="responseMessage">
                  Your Response (Optional):
                </label>
                <textarea
                  id="responseMessage"
                  value={responseMessage}
                  onChange={(e) => setResponseMessage(e.target.value)}
                  placeholder="Add a message to the bidder..."
                  rows="3"
                  maxLength="500"
                />
                <small>{responseMessage.length}/500</small>
              </div>

              <div className="modal-actions">
                <button
                  className="btn-reject"
                  onClick={() => handleRejectBid(selectedBid._id)}
                  disabled={isResponding}
                >
                  {isResponding ? "Processing..." : "Reject Bid"}
                </button>
                <button
                  className="btn-accept"
                  onClick={() => handleAcceptBid(selectedBid._id)}
                  disabled={isResponding}
                >
                  {isResponding ? "Processing..." : "Accept Bid"}
                </button>
              </div>

              <div className="modal-note">
                <p>
                  <strong>Note:</strong> Accepting this bid will create an order
                  and end the auction for this content.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SellerBidManagement;
